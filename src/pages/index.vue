<template>
  <div>
    <!-- Header Navigation -->
    <v-app-bar
      color="white"
      elevation="1"
      flat
      class="px-4"
    >
      <v-img
        src="@/assets/logo.png"
        alt="Flawless Aesthetics Logo"
        max-width="60"
        max-height="60"
      />

      <v-spacer />

      <v-app-bar-nav-icon
        class="hidden-md-and-up"
        @click="drawer = !drawer"
      />

      <v-btn
        class="hidden-sm-and-down"
        color="primary"
        href="tel:7279067397"
        variant="text"
      >
        <v-icon start>mdi-phone</v-icon>
        Call Now
      </v-btn>
    </v-app-bar>

    <!-- Hero Section -->
    <div class="hero-section" :style="videoError ? `background-image: url(${heroFallback})` : ''">
      <video
        v-if="!videoError"
        ref="videoRef"
        class="hero-video"
        autoplay
        muted
        loop
        playsinline
        preload="metadata"
        :poster="heroFallback"
      >
        <source :src="heroVideo" type="video/mp4">
        Your browser does not support the video tag.
      </video>
      <div class="hero-overlay">
        <v-container>
          <v-row align="center" justify="center" class="text-center">
            <v-col cols="12" md="8">
              <h1 class="hero-title text-white mb-4">
                Revitalize Your Beauty at Flawless Aesthetics and Day Spa
              </h1>
              <p class="hero-subtitle text-white mb-6">
                Experience rejuvenating treatments like chemical peels, microneedling, and massages at our day spa.
              </p>
              <v-btn
                class="mb-4"
                color="white"
                href="tel:7279067397"
                size="large"
                variant="outlined"
              >
                (*************
              </v-btn>
              <p class="text-white">
                1016 Ponce de Leon Blvd, Suite 1, Belleair, FL 33756
              </p>
            </v-col>
          </v-row>
        </v-container>
      </div>
    </div>

    <!-- About Section -->
    <v-container class="py-16">
      <v-row align="center">
        <v-col cols="12" md="6">
          <h2 class="section-title mb-4">Aesthetics Spa</h2>
          <p class="section-text">
            Experience rejuvenation and relaxation with our expert services, including chemical peels, microneedling, cryotherapy, and massage, tailored to enhance your natural beauty and well-being.
          </p>
        </v-col>
        <v-col cols="12" md="6">
          <v-img
            src="@/assets/spa-treatment.jpg"
            alt="Spa Treatment"
            aspect-ratio="1.2"
            class="rounded-lg"
          />
        </v-col>
      </v-row>
    </v-container>

    <!-- Services Section -->
    <v-container fluid class="services-section py-16">
      <v-container>
        <v-row>
          <v-col cols="12" class="text-center mb-8">
            <h2 class="section-title mb-4">Spa Services Offered</h2>
            <p class="section-text">
              Explore our rejuvenating treatments including chemical peels, microneedling, and relaxing massages.
            </p>
          </v-col>
        </v-row>

        <v-row>
          <v-col cols="12" md="4" class="mb-8">
            <v-card class="service-card" elevation="0">
              <v-card-title class="service-title">
                Soothing Massage Therapy
              </v-card-title>
              <v-card-text class="service-text">
                Relax and unwind with our therapeutic massage services designed to relieve tension.
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="4" class="mb-8">
            <v-card class="service-card" elevation="0">
              <v-card-title class="service-title">
                Revitalizing Chemical Peels
              </v-card-title>
              <v-card-text class="service-text">
                Transform your skin with our effective chemical peel treatments tailored to your needs.
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="4" class="mb-8">
            <v-card class="service-card" elevation="0">
              <v-card-title class="service-title">
                Advanced Microneedling
              </v-card-title>
              <v-card-text class="service-text">
                Rejuvenate your skin with our advanced microneedling treatments for improved texture and tone.
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-container>

    <!-- Navigation Drawer for Mobile -->
    <v-navigation-drawer
      v-model="drawer"
      location="right"
      temporary
    >
      <v-list>
        <v-list-item
          href="tel:7279067397"
          prepend-icon="mdi-phone"
          subtitle="(*************"
          title="Call Now"
        />
        <v-list-item
          prepend-icon="mdi-map-marker"
          subtitle="1016 Ponce de Leon Blvd, Suite 1"
          title="Visit Us"
        />
      </v-list>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import heroVideo from '@/assets/5669040-hd_1280_720_30fps.mp4'
  import heroFallback from '@/assets/hero-background.jpg'

  const drawer = ref(false)
  const videoError = ref(false)
  const videoRef = ref(null)

  onMounted(() => {
    if (videoRef.value) {
      videoRef.value.addEventListener('error', () => {
        videoError.value = true
      })

      videoRef.value.addEventListener('loadeddata', () => {
        console.log('Video loaded successfully')
      })
    }
  })
</script>

<style scoped>
.hero-section {
  position: relative;
  height: 600px;
  overflow: hidden;
}

.hero-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translate(-50%, -50%);
  object-fit: cover;
  z-index: -1;
}

.hero-overlay {
  background: rgba(0, 0, 0, 0.4);
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3rem;
  font-weight: 300;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 300;
  color: #666;
  text-align: center;
}

.section-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666;
  text-align: center;
}

.services-section {
  background-color: #f8f6f3;
}

.service-card {
  background: transparent;
  text-align: center;
  padding: 2rem 1rem;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 400;
  color: #666;
  text-align: center;
  margin-bottom: 1rem;
}

.service-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  text-align: center;
}

@media (max-width: 960px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }
}
</style>
